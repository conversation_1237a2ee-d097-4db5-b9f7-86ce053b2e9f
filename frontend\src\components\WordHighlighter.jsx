import { useState, useEffect, useRef } from 'react';
import { transcriptionAPI } from '../services/api';

export const WordHighlighter = ({
  mediaFile,
  transcription,
  playerRef,
  isEnabled = true,
  className = ''
}) => {
  const [wordLevelData, setWordLevelData] = useState([]);
  const [currentWordIndex, setCurrentWordIndex] = useState(-1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentTime, setCurrentTime] = useState(0);
  const overlayRef = useRef(null);

  // Load word-level VTT data
  useEffect(() => {
    if (!mediaFile || !transcription || !transcription.has_word_level_vtt || !isEnabled) {
      return;
    }

    const loadWordLevelData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const wordVttUrl = transcriptionAPI.getWordLevelSubtitleFileUrl(mediaFile.id);
        const response = await fetch(wordVttUrl);
        
        if (!response.ok) {
          throw new Error('Failed to load word-level subtitle data');
        }
        
        const vttText = await response.text();
        const parsedData = parseWordLevelVTT(vttText);
        setWordLevelData(parsedData);
      } catch (err) {
        console.error('Error loading word-level data:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    loadWordLevelData();
  }, [mediaFile, transcription, isEnabled]);

  // Set up time update listener
  useEffect(() => {
    if (!playerRef?.current || !isEnabled) {
      return;
    }

    const handleTimeUpdate = () => {
      const playerCurrentTime = playerRef.current.currentTime();
      setCurrentTime(playerCurrentTime);

      // Update word highlighting if we have word-level data
      if (wordLevelData.length > 0) {
        const activeWordIndex = findActiveWordIndex(playerCurrentTime, wordLevelData);
        if (activeWordIndex !== currentWordIndex) {
          console.log('WordHighlighter: Word changed to index', activeWordIndex,
            activeWordIndex >= 0 ? wordLevelData[activeWordIndex] : null);
          setCurrentWordIndex(activeWordIndex);
        }
      }
    };

    const player = playerRef.current;
    player.on('timeupdate', handleTimeUpdate);

    return () => {
      player.off('timeupdate', handleTimeUpdate);
    };
  }, [playerRef, wordLevelData, currentWordIndex, isEnabled]);

  // Parse VTT content to extract word-level timing
  const parseWordLevelVTT = (vttText) => {
    const lines = vttText.split('\n');
    const words = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i].trim();
      
      // Skip WEBVTT header and empty lines
      if (line === 'WEBVTT' || line === '') {
        i++;
        continue;
      }

      // Check if this is a cue number
      if (/^\d+$/.test(line)) {
        i++; // Move to timing line
        
        if (i < lines.length) {
          const timingLine = lines[i].trim();
          const timingMatch = timingLine.match(/(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})/);
          
          if (timingMatch) {
            const startTime = parseVTTTime(timingMatch[1]);
            const endTime = parseVTTTime(timingMatch[2]);
            
            i++; // Move to text line
            if (i < lines.length) {
              const textLine = lines[i].trim();
              
              // Extract word from VTT cue (remove speaker labels and styling)
              const wordMatch = textLine.match(/<c\.word-highlight>(.*?)<\/c>/);
              const word = wordMatch ? wordMatch[1] : textLine.replace(/<[^>]*>/g, '');
              
              if (word) {
                words.push({
                  word: word.trim(),
                  start: startTime,
                  end: endTime,
                  index: words.length
                });
              }
            }
          }
        }
      }
      i++;
    }

    return words;
  };

  // Convert VTT time format to seconds
  const parseVTTTime = (timeString) => {
    const [hours, minutes, seconds] = timeString.split(':');
    return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds);
  };

  // Find the currently active word based on playback time
  const findActiveWordIndex = (currentTime, words) => {
    for (let i = 0; i < words.length; i++) {
      if (currentTime >= words[i].start && currentTime <= words[i].end) {
        return i;
      }
    }
    return -1;
  };

  // Get current segment text with highlighted words
  const getCurrentSegmentWithHighlights = () => {
    if (!transcription?.segments) {
      return null;
    }

    // If no word is currently active, show the current segment based on player time
    if (currentWordIndex === -1) {
      if (!playerRef?.current) return null;

      const currentTime = playerRef.current.currentTime();
      const currentSegment = transcription.segments.find(segment =>
        currentTime >= segment.start && currentTime <= segment.end
      );

      if (currentSegment) {
        return {
          segment: currentSegment,
          highlightedText: currentSegment.text,
          currentWord: null
        };
      }
      return null;
    }

    const currentWord = wordLevelData[currentWordIndex];
    if (!currentWord) return null;

    // Find the segment that contains this word (with some tolerance for timing differences)
    const currentSegment = transcription.segments.find(segment => {
      const tolerance = 0.5; // 500ms tolerance
      return currentWord.start >= (segment.start - tolerance) &&
             currentWord.start <= (segment.end + tolerance);
    });

    if (!currentSegment) {
      // Fallback: find the closest segment
      let closestSegment = transcription.segments[0];
      let minDistance = Math.abs(currentWord.start - closestSegment.start);

      transcription.segments.forEach(segment => {
        const distance = Math.abs(currentWord.start - segment.start);
        if (distance < minDistance) {
          minDistance = distance;
          closestSegment = segment;
        }
      });

      // Use closest segment if within reasonable range (10 seconds)
      if (minDistance <= 10) {
        return {
          segment: closestSegment,
          highlightedText: closestSegment.text.replace(
            new RegExp(`\\b${escapeRegExp(currentWord.word)}\\b`, 'gi'),
            `<span class="current-word-highlight">${currentWord.word}</span>`
          ),
          currentWord
        };
      }

      return null;
    }

    // Create highlighted text by highlighting only the current word
    let highlightedText = currentSegment.text;

    // Use a more precise approach to highlight the exact current word
    const wordRegex = new RegExp(`\\b${escapeRegExp(currentWord.word)}\\b`, 'gi');
    let matchCount = 0;
    highlightedText = highlightedText.replace(wordRegex, (match) => {
      matchCount++;
      // Only highlight the first occurrence to avoid multiple highlights
      if (matchCount === 1) {
        return `<span class="current-word-highlight">${match}</span>`;
      }
      return match;
    });

    return {
      segment: currentSegment,
      highlightedText,
      currentWord
    };
  };

  // Escape special regex characters
  const escapeRegExp = (string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  };

  if (!isEnabled || !transcription?.has_word_level_vtt) {
    return null;
  }

  if (isLoading) {
    return (
      <div className={`word-highlighter ${className}`}>
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-sm text-gray-600">Loading word highlighting...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`word-highlighter ${className}`}>
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">Error loading word highlighting: {error}</p>
        </div>
      </div>
    );
  }

  const segmentData = getCurrentSegmentWithHighlights();

  // Get current segment for display - always try to show something
  const getCurrentSegmentForDisplay = () => {
    if (segmentData) {
      console.log('WordHighlighter: Using segmentData:', segmentData.segment.text.substring(0, 50));
      return segmentData;
    }

    // Fallback: show current segment based on player time without word highlighting
    if (!transcription?.segments) {
      console.log('WordHighlighter: No segments available');
      return null;
    }

    const currentSegment = transcription.segments.find(segment =>
      currentTime >= segment.start && currentTime <= segment.end
    );

    if (currentSegment) {
      console.log('WordHighlighter: Using fallback segment:', currentSegment.text.substring(0, 50));
      return {
        segment: currentSegment,
        highlightedText: currentSegment.text,
        currentWord: null
      };
    }

    // Last resort: find the closest segment
    if (transcription.segments.length > 0) {
      let closestSegment = transcription.segments[0];
      let minDistance = Math.abs(currentTime - closestSegment.start);

      transcription.segments.forEach(segment => {
        const distance = Math.abs(currentTime - segment.start);
        if (distance < minDistance) {
          minDistance = distance;
          closestSegment = segment;
        }
      });

      if (minDistance <= 5) { // Within 5 seconds
        console.log('WordHighlighter: Using closest segment:', closestSegment.text.substring(0, 50));
        return {
          segment: closestSegment,
          highlightedText: closestSegment.text,
          currentWord: null
        };
      }
    }

    console.log('WordHighlighter: No suitable segment found for time:', currentTime);
    return null;
  };

  const displayData = getCurrentSegmentForDisplay();

  // Add debug logging
  useEffect(() => {
    console.log('WordHighlighter render:', {
      hasDisplayData: !!displayData,
      currentWordIndex,
      currentTime,
      wordLevelDataLength: wordLevelData.length,
      isEnabled,
      hasTranscription: !!transcription,
      hasSegments: !!transcription?.segments?.length
    });
  }, [displayData, currentWordIndex, currentTime, wordLevelData.length, isEnabled, transcription]);

  // Debug what we're trying to display
  if (displayData) {
    console.log('WordHighlighter: About to display:', {
      text: displayData.highlightedText,
      segmentStart: displayData.segment.start,
      segmentEnd: displayData.segment.end,
      currentTime
    });
  } else {
    console.log('WordHighlighter: No displayData available');
  }

  return (
    <div className={`word-highlighter ${className}`} ref={overlayRef} style={{ zIndex: 1000 }}>
      {/* Subtitle-style display showing full segment with word highlighting */}
      {displayData ? (
        <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 max-w-5xl w-full px-4 z-50">
          <div className="bg-black/85 backdrop-blur-sm rounded-lg px-6 py-4 text-center shadow-lg border border-white/20">
            <div
              className="text-xl leading-relaxed text-white font-medium"
              dangerouslySetInnerHTML={{ __html: displayData.highlightedText }}
            />
          </div>
        </div>
      ) : (
        /* Show a test subtitle when no data is available */
        <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 max-w-5xl w-full px-4 z-50">
          <div className="bg-red-600/85 backdrop-blur-sm rounded-lg px-6 py-4 text-center shadow-lg border border-white/20">
            <div className="text-xl leading-relaxed text-white font-medium">
              No subtitle data available (Time: {currentTime.toFixed(1)}s)
            </div>
          </div>
        </div>
      )}

      {/* Debug info (can be removed in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 left-4 text-xs text-yellow-300 bg-black/60 p-2 rounded max-w-md z-40">
          Words loaded: {wordLevelData.length} | Current word index: {currentWordIndex}
          {displayData ? ` | Segment: "${displayData.segment.text.substring(0, 30)}..."` : ' | No segment found'}
          {currentWordIndex >= 0 && wordLevelData[currentWordIndex] && (
            <div>Current word: "{wordLevelData[currentWordIndex].word}" at {wordLevelData[currentWordIndex].start.toFixed(2)}s</div>
          )}
        </div>
      )}
    </div>
  );
};

export default WordHighlighter;
